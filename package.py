# -*- coding: utf-8 -*-
"""
📦 Instagram任务管理器 - 精简打包程序
功能：一键打包，包含所有必要文件
"""

import os
import sys
import subprocess
from pathlib import Path

def package_app():
    """打包应用程序"""
    print("📦 ins雷电中控 - 打包程序")
    print("=" * 50)

    # 询问是否清理旧的构建文件
    clean_build = input("是否清理旧的构建文件? (y/n, 默认n): ").strip().lower()
    if clean_build == 'y':
        import shutil
        build_dir = Path(__file__).parent / "build"
        dist_dir = Path(__file__).parent / "dist"

        if build_dir.exists():
            shutil.rmtree(build_dir)
            print("🗑️  已清理build目录")

        if dist_dir.exists():
            shutil.rmtree(dist_dir)
            print("🗑️  已清理dist目录")
    
    project_root = Path(__file__).parent
    
    # 1. 检查必要文件
    print("🔍 检查必要文件...")
    
    required_files = {
        "main.py": "主程序文件",
        "img": "图片资源目录（图色识别必需）",
        "app_config.json": "配置文件"
    }
    
    missing_files = []
    for file_path, description in required_files.items():
        full_path = project_root / file_path
        if full_path.exists():
            if file_path == "img":
                png_count = len(list(full_path.glob("*.png")))
                print(f"  ✅ {description} ({png_count}个图片)")
            else:
                print(f"  ✅ {description}")
        else:
            print(f"  ❌ {description} - 缺失")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    # 2. 安装PyInstaller
    print("\n📦 检查PyInstaller...")
    try:
        import PyInstaller
        print("  ✅ PyInstaller已安装")
    except ImportError:
        print("  📥 正在安装PyInstaller...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
            print("  ✅ PyInstaller安装完成")
        except subprocess.CalledProcessError:
            print("  ❌ PyInstaller安装失败")
            return False
    
    # 3. 执行打包
    print("\n🔨 开始打包...")

    # 使用绝对路径确保文件被正确包含
    img_abs_path = str(project_root / "img")
    config_abs_path = str(project_root / "config")
    data_abs_path = str(project_root / "data")
    app_config_abs_path = str(project_root / "app_config.json")

    print(f"📁 img绝对路径: {img_abs_path}")

    cmd = [
        "pyinstaller",
        "--onefile",                              # 单文件
        "--windowed",                             # 隐藏控制台
        "--clean",                                # 🎯 清理缓存
        "--name=ins雷电中控",                     # 程序名
        f"--add-data={img_abs_path};img",         # 🎯 图片资源（绝对路径）
        f"--add-data={config_abs_path};config",   # 配置目录
        f"--add-data={data_abs_path};data",       # 数据目录
        f"--add-data={app_config_abs_path};.",    # 配置文件
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui",
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=requests",
        "--hidden-import=psutil",
        "--hidden-import=sqlite3",
        "main.py"
    ]
    
    try:
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 打包完成")
            
            # 检查输出文件
            exe_file = project_root / "dist" / "ins雷电中控.exe"
            if exe_file.exists():
                size_mb = exe_file.stat().st_size / (1024 * 1024)
                print(f"📦 输出文件: {exe_file}")
                print(f"📏 文件大小: {size_mb:.1f} MB")
                
                # 验证关键文件
                print("\n🔍 验证打包结果...")
                dist_img = project_root / "dist" / "img"
                if dist_img.exists():
                    png_count = len(list(dist_img.glob("*.png")))
                    print(f"  ✅ img目录已包含 ({png_count}个图片)")
                else:
                    print("  ❌ img目录缺失 - 图色识别将无法工作")
                
                dist_config = project_root / "dist" / "app_config.json"
                if dist_config.exists():
                    print("  ✅ 配置文件已包含")
                else:
                    print("  ❌ 配置文件缺失")
                
                return True
            else:
                print("❌ 未找到输出文件")
                return False
        else:
            print("❌ 打包失败")
            if result.stderr:
                print(f"错误信息: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 打包过程出错: {e}")
        return False

def main():
    """主函数"""
    try:
        success = package_app()
        
        if success:
            print("\n🎉 打包成功！")
            print("💡 输出文件: dist/ins雷电中控.exe")
            print("💡 可以将exe文件分发给用户使用")
        else:
            print("\n💥 打包失败")
            print("💡 请检查错误信息并重试")
        
        input("\n按Enter键退出...")
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
    except Exception as e:
        print(f"\n💥 程序出错: {e}")

if __name__ == "__main__":
    main()
