<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
    <class>MainWindow</class>
    <widget class="QMainWindow" name="MainWindow">
        <property name="geometry">
            <rect>
                <x>0</x>
                <y>0</y>
                <width>680</width>
                <height>320</height>
            </rect>
        </property>
        <property name="minimumSize">
            <size>
                <width>680</width>
                <height>320</height>
            </size>
        </property>
        <property name="maximumSize">
            <size>
                <width>680</width>
                <height>320</height>
            </size>
        </property>
        <property name="windowTitle">
            <string>酷烧云网络验证 V3.0</string>
        </property>
        <property name="windowIcon">
            <iconset>
                <normaloff>logo.ico</normaloff>logo.ico
            </iconset>
        </property>
        <widget class="QWidget" name="centralwidget">
            <widget class="QGroupBox" name="groupBox">
                <property name="geometry">
                    <rect>
                        <x>0</x>
                        <y>0</y>
                        <width>340</width>
                        <height>280</height>
                    </rect>
                </property>
                <property name="font">
                    <font>
                        <pointsize>12</pointsize>
                    </font>
                </property>
                <property name="title">
                    <string>认 证</string>
                </property>
                <widget class="QWidget" name="formLayoutWidget">
                    <property name="geometry">
                        <rect>
                            <x>10</x>
                            <y>50</y>
                            <width>321</width>
                            <height>161</height>
                        </rect>
                    </property>
                    <layout class="QFormLayout" name="formLayout">
                        <property name="sizeConstraint">
                            <enum>QLayout::SetDefaultConstraint</enum>
                        </property>
                        <property name="horizontalSpacing">
                            <number>10</number>
                        </property>
                        <property name="verticalSpacing">
                            <number>25</number>
                        </property>
                        <property name="leftMargin">
                            <number>5</number>
                        </property>
                        <item row="0" column="0">
                            <widget class="QLabel" name="label">
                                <property name="text">
                                    <string>卡号</string>
                                </property>
                            </widget>
                        </item>
                        <item row="0" column="1">
                            <widget class="QLineEdit" name="lineEdit">
                                <property name="font">
                                    <font>
                                        <family>Consolas</family>
                                        <pointsize>12</pointsize>
                                    </font>
                                </property>
                                <property name="maxLength">
                                    <number>64</number>
                                </property>
                                <property name="placeholderText">
                                    <string>请输入卡号</string>
                                </property>
                            </widget>
                        </item>
                        <item row="1" column="0">
                            <widget class="QLabel" name="label_2">
                                <property name="text">
                                    <string>线路</string>
                                </property>
                            </widget>
                        </item>
                        <item row="1" column="1">
                            <widget class="QComboBox" name="comboBox">
                                <property name="font">
                                    <font>
                                        <pointsize>12</pointsize>
                                    </font>
                                </property>
                                <item>
                                    <property name="text">
                                        <string>认证线路 ①</string>
                                    </property>
                                </item>
                                <item>
                                    <property name="text">
                                        <string>认证线路 ②</string>
                                    </property>
                                </item>
                                <item>
                                    <property name="text">
                                        <string>认证线路 ③</string>
                                    </property>
                                </item>
                            </widget>
                        </item>
                        <item row="2" column="1">
                            <widget class="QPushButton" name="startButton">
                                <property name="font">
                                    <font>
                                        <pointsize>14</pointsize>
                                    </font>
                                </property>
                                <property name="text">
                                    <string>启动 / Start</string>
                                </property>
                            </widget>
                        </item>
                    </layout>
                </widget>
                <widget class="QWidget" name="horizontalLayoutWidget">
                    <property name="geometry">
                        <rect>
                            <x>10</x>
                            <y>210</y>
                            <width>321</width>
                            <height>51</height>
                        </rect>
                    </property>
                    <layout class="QHBoxLayout" name="horizontalLayout">
                        <item>
                            <widget class="QPushButton" name="tryButton">
                                <property name="font">
                                    <font>
                                        <pointsize>12</pointsize>
                                    </font>
                                </property>
                                <property name="text">
                                    <string>我要试用</string>
                                </property>
                            </widget>
                        </item>
                        <item>
                            <widget class="QPushButton" name="buyButton">
                                <property name="font">
                                    <font>
                                        <pointsize>12</pointsize>
                                    </font>
                                </property>
                                <property name="text">
                                    <string>在线购卡</string>
                                </property>
                            </widget>
                        </item>
                    </layout>
                </widget>
            </widget>
            <widget class="QGroupBox" name="groupBox_2">
                <property name="geometry">
                    <rect>
                        <x>360</x>
                        <y>0</y>
                        <width>320</width>
                        <height>280</height>
                    </rect>
                </property>
                <property name="font">
                    <font>
                        <pointsize>12</pointsize>
                    </font>
                </property>
                <property name="title">
                    <string>公 告</string>
                </property>
                <widget class="QTextBrowser" name="textBrowser">
                    <property name="geometry">
                        <rect>
                            <x>10</x>
                            <y>30</y>
                            <width>301</width>
                            <height>241</height>
                        </rect>
                    </property>
                    <property name="font">
                        <font>
                            <pointsize>10</pointsize>
                        </font>
                    </property>
                    <property name="frameShape">
                        <enum>QFrame::Box</enum>
                    </property>
                    <property name="lineWidth">
                        <number>3</number>
                    </property>
                    <property name="midLineWidth">
                        <number>0</number>
                    </property>
                    <property name="html">
                        <string>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
                            &lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;meta
                            charset=&quot;utf-8&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
                            p, li { white-space: pre-wrap; }
                            hr { height: 1px; border-width: 0; }
                            li.unchecked::marker { content: &quot;\2610&quot;; }
                            li.checked::marker { content: &quot;\2612&quot;; }
                            &lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:'Microsoft YaHei UI';
                            font-size:10pt; font-weight:400; font-style:normal;&quot;&gt;
                            &lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px;
                            -qt-block-indent:0; text-indent:0px;&quot;&gt; &lt;/p&gt;
                            &lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px;
                            -qt-block-indent:0; text-indent:0px;&quot;&gt; &lt;/p&gt;&lt;/body&gt;&lt;/html&gt;
                        </string>
                    </property>
                </widget>
            </widget>
            <widget class="Line" name="line">
                <property name="geometry">
                    <rect>
                        <x>0</x>
                        <y>285</y>
                        <width>680</width>
                        <height>12</height>
                    </rect>
                </property>
                <property name="lineWidth">
                    <number>0</number>
                </property>
                <property name="midLineWidth">
                    <number>1</number>
                </property>
                <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                </property>
            </widget>
            <widget class="Line" name="line_2">
                <property name="geometry">
                    <rect>
                        <x>340</x>
                        <y>10</y>
                        <width>20</width>
                        <height>281</height>
                    </rect>
                </property>
                <property name="lineWidth">
                    <number>0</number>
                </property>
                <property name="midLineWidth">
                    <number>1</number>
                </property>
                <property name="orientation">
                    <enum>Qt::Vertical</enum>
                </property>
            </widget>
        </widget>
        <widget class="QStatusBar" name="statusBar"/>
    </widget>
    <resources/>
    <connections/>
</ui>
