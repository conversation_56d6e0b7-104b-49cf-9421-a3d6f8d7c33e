# coding=utf-8
import configparser
import os
import sys
import threading
import time
import warnings
from functools import partial

from PyQt6 import uic
from PyQt6.QtCore import QTimer
from PyQt6.QtWidgets import QApplication, QLabel, QLineEdit, QMessageBox, QMainWindow

from authsdk import Auth


def work(auth):
    """
    删除该函数内的示例代码，将您的脚本写到此处
    为什么写到这里？也就是在认证成功后启动了该线程，没有认证成功这里的代码不会被执行
    """

    '''
    超级防破解实现：
    1.关键性参数使用远程变量读写
    2.把涉及计算、拼接相关的代码封装成远程函数在云端计算
    云端数据的每一次调用都需要签名和时间鉴权，因此不管抓包还是爆破都没有任何用处
    '''
    print('卡号备注：' + auth.remark)

    fun_res = auth.run_function('远程函数示例', '100, 200')
    if fun_res or fun_res == '':
        print('远程函数执行的结果是：' + fun_res)

    '''
    # 读取远程变量
    value = auth.get_value('netVal008')
    if value or value == '':
        print('远程变量的值：' + value)

    # 更新云端变量
    new_value = 'Python远程变量更新测试 ' + auth.timestamp()
    if auth.set_value('netVal008', new_value):
        print('远程变量更新成功')
    '''

    for i in range(1000):
        time.sleep(1)
        ui.statusBar.showMessage(f'主脚本已运行 {i} 秒', 1000)


class MyMainWindow(QMainWindow):

    def __init__(self, auth):
        warnings.filterwarnings("ignore", category=DeprecationWarning)
        super().__init__()
        self.auth = auth

        uic.loadUi('mainUI.ui', self)
        self.comboBox.currentIndexChanged.connect(self.line_changed)
        self.read_config()

    def line_changed(self):
        index = self.comboBox.currentIndex()
        api_map = {
            0: 'http://api.kushao.net/v3/',
            1: 'http://api.kushao.018888.xyz/v3/'
        }
        self.auth.api = api_map.get(index, 'http://api.ks.186777.xyz/v3/')

    def read_config(self):
        path = self.get_path()
        if os.path.exists(path):
            config = configparser.ConfigParser()
            config.read(path)
            self.lineEdit.setText(config.get('Config', 'auth'))
            self.comboBox.setCurrentIndex(int(config.get('Config', 'line')))

            self.line_changed()
        else:
            self.auth.api = self.auth.get_fastest_url()

    def save_config(self):
        config = configparser.ConfigParser()
        config['Config'] = {'line': self.comboBox.currentIndex(), 'auth': self.lineEdit.text()}
        with open(self.get_path(), 'w') as configfile:
            config.write(configfile)

    @staticmethod
    def get_path():
        user_documents = os.path.expanduser(r"~\Documents")
        return os.path.join(user_documents, 'auth_config.ini')

    def closeEvent(self, event):
        reply = QMessageBox.question(self, '确认关闭', '确定要关闭吗？',
                                     QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
            self.save_config()
            os._exit(0)
            event.accept()
        else:
            event.ignore()


def start_btn_click(auth, ui):
    try:
        # 单卡号模式只需传入卡号；如果账号密码认证，第一个参数为账号，第二个参数为密码
        result = auth.verify(ui.lineEdit.text())

        # 把认证结果显示在状态栏右侧
        myLabel = ui.statusBar.findChild(QLabel, "myLabel")
        if result['code'] == 200:
            ui.startButton.setEnabled(False)
            # 把认证结果显示在状态栏右侧
            myLabel.setStyleSheet("color: green;")
            if auth.mode == 0:
                myLabel.setText('有效期到：' + result['end_date'])
            else:
                myLabel.setText('可用次数剩余 ' + result['surplus_count'] + ' 次')
        elif result['code'] == 104:
            # 检测到换机则自动调用换机相关方法
            auth.change_device(ui.lineEdit.text(), '', ui)
        else:
            myLabel.setStyleSheet("color: red;")
            myLabel.setText('认证失败：' + result['msg'])

        if result['code'] == 200:
            # 认证成功，启动主脚本
            work_thread = threading.Thread(target=work, kwargs={'auth': auth}, daemon=True)
            work_thread.start()
            # 调用心跳轮询
            heart_beat_thread = threading.Thread(target=auth.heart_beat)
            heart_beat_thread.start()
    except Exception:
        QMessageBox.warning(ui, '提示信息', '请选择其他认证线路再试', QMessageBox.StandardButton.Ok)


def buy_btn_click(auth, ui):
    if auth.shop is None:
        QMessageBox.warning(ui, '提示信息', '应用信息获取失败，请重启程序尝试', QMessageBox.StandardButton.Ok)
    else:
        if auth.is_valid_url(auth.shop):
            auth.open_url(auth.shop)


def try_btn_click(auth, ui):
    try:
        result = auth.reg_or_try()  # 单卡模式获取试用无需传参数，如果注册账号密码则传入账号和密码

        if auth.rsa_decrypt(result['code']) == '200':
            ui.tryButton.setEnabled(False)
            auth_code = auth.rsa_decrypt(result['authCode'])
            used_count = auth.rsa_decrypt(result['usedCount'])
            trial_count = auth.rsa_decrypt(result['trialCount'])

            if auth.mode == 0:
                QMessageBox.information(ui, '提示信息', f"""试用卡号：{auth_code}
系统赠送 {auth.rsa_decrypt(result['trialTime'])} 分钟体验时间，可免费使用到：{auth.rsa_decrypt(result['endDate'])}
您已获取 {used_count} 次试用，还能获取 {str(int(trial_count) - int(used_count))} 次
                """, QMessageBox.StandardButton.Ok)
            else:
                QMessageBox.information(ui, '提示信息', f"""试用卡号：{auth_code}            
系统免费赠送 {auth.rsa_decrypt(result['surplusCount'])} 次使用机会，您已获取 {used_count} 次试用，还能获取 {str(int(trial_count) - int(used_count))} 次
                """, QMessageBox.StandardButton.Ok)

            auth_input: QLineEdit = ui.lineEdit
            auth_input.setText(auth_code)
        else:
            QMessageBox.warning(ui, '提示信息', auth.rsa_decrypt(result['msg']), QMessageBox.StandardButton.Ok)
    except Exception:
        QMessageBox.warning(ui, '提示信息', '请选择其他认证线路再试', QMessageBox.StandardButton.Ok)


def get_appinfo(auth, ui):
    try:
        result = auth.appinfo(0)
        code = auth.rsa_decrypt(result['code'])
        if code == '200':
            print('-' * 15, '这部分代码目的是演示如何得到这些参数，可删除用不到的部分', '-' * 15)
            app_name = auth.rsa_decrypt(result['appName'])
            print('项目名称：' + app_name)
            version = auth.rsa_decrypt(result['version'])
            print('后台版本：' + version)
            app_status = auth.rsa_decrypt(result['appStatus'])
            print('项目开关：' + app_status)
            notice_status = auth.rsa_decrypt(result['noticeStatus'])
            print('公告开关：' + notice_status)
            trial_count = auth.rsa_decrypt(result['trialCount'])
            print('试用次数：' + trial_count)
            trial_time = auth.rsa_decrypt(result['trialTime'])
            print('试用时长：' + trial_time)
            change_time = auth.rsa_decrypt(result['changeTime'])
            print('顶号扣时：' + change_time)
            web_site = auth.rsa_decrypt(result['webSite'])
            print('官方网址：' + web_site)
            shop_link = auth.rsa_decrypt(result['shopLink'])
            print('专属网店：' + shop_link)
            download_link = auth.rsa_decrypt(result['downloadLink'])
            print('下载网址：' + download_link)
            notice = auth.rsa_decrypt(result['notice'])
            print('公告信息：' + notice)
            contact_info = auth.rsa_decrypt(result['contactInfo'])
            print('客服信息：' + contact_info)

            # 将专属网店网址赋值给 auth.shop ，便于在点击 在线购卡按钮时调用
            auth.shop = shop_link

            # 检测更新
            if float(version) > auth.version and auth.is_valid_url(download_link):
                print('检测到新版本自动打开下载网址')
                auth.open_url(download_link)
                if auth.update_mode == 1:
                    sys.exit()

            # 远程公告
            if notice_status == '1':
                ui.textBrowser.setHtml(notice)

    except Exception:
        ui.textBrowser.setHtml('<h3 style="color: red">请选择其他认证线路再试</h3>')


if __name__ == '__main__':
    auth = Auth()
    app = QApplication(sys.argv)
    ui = MyMainWindow(auth)
    myLabel = QLabel(objectName="myLabel")
    ui.statusBar.addPermanentWidget(myLabel)
    ui.show()

    '''
    以卡充卡示例：
    auth.recharge('卡号或账号', '用于充值的卡号')
    
    账号模式修改密码示例：
    auth.change_password('账号', '旧密码', '新密码')
    '''

    ui.startButton.clicked.connect(partial(start_btn_click, auth, ui))
    ui.tryButton.clicked.connect(partial(try_btn_click, auth, ui))
    ui.buyButton.clicked.connect(partial(buy_btn_click, auth, ui))

    # 延迟 1 秒获取应用信息，避免加载窗体时卡顿
    # 更高级的应该把所有耗时操作都放到子线程执行，这样才能避免界面阻塞，当然这不属于网络验证范畴，各位就各显神通吧
    QTimer.singleShot(1, partial(get_appinfo, auth, ui))

    sys.exit(app.exec())
